# OpenStream Landing Page

**All In One Streaming Hub - Access unlimited movies, series, and anime**

[![Built with Next.js](https://img.shields.io/badge/Built%20with-Next.js-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![Powered by React](https://img.shields.io/badge/Powered%20by-React-blue?style=for-the-badge&logo=react)](https://reactjs.org/)
[![Styled with Tailwind](https://img.shields.io/badge/Styled%20with-Tailwind%20CSS-38B2AC?style=for-the-badge&logo=tailwind-css)](https://tailwindcss.com/)

## Overview

OpenStream is your ultimate destination for unlimited movies, series, and anime streaming. Access premium content from top OTT platforms like Netflix, Prime Video, Disney+ and high-speed BDix servers - completely free with no ads and HD quality.

## Features

- 🎬 **Unlimited Content**: Access movies, TV series, anime, and live TV
- 🚫 **Ad-Free Experience**: Enjoy uninterrupted streaming without advertisements
- 🔥 **HD Quality**: Stream in 4K, 1080p, 720p, and 480p quality
- ⚡ **Fast Servers**: Multiple high-speed servers for buffer-free streaming
- 📱 **Multi-Platform**: Android devices, Android TV, and Chromecast support
- 💾 **Offline Download**: Download content for offline viewing
- 🌐 **Global Access**: Available worldwide with 24/7 service

## Supported Platforms

### OTT Platforms
- Netflix
- Prime Video
- Disney+
- Hulu
- MovieBox

### BDix Servers
- ICC FTP
- DFlix FTP
- Circle FTP

### Anime Servers
- HiAnime
- 9Anime

## Getting Started

1. **Download the App**: Get the latest CloudStream APK
2. **Install**: Enable "Unknown Sources" and install the APK
3. **Connect Server**: Use our one-click server connection
4. **Start Streaming**: Enjoy unlimited content

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI
- **Icons**: Lucide React
- **Performance**: Server-Side Rendering (SSR)
- **SEO**: Comprehensive meta tags and structured data

## Performance Optimizations

### SEO Enhancements
- ✅ **Server-Side Rendering**: Converted from client-side to SSR for better SEO
- ✅ **Enhanced Meta Tags**: Comprehensive title, description, and keywords
- ✅ **Open Graph**: Rich social media previews
- ✅ **Twitter Cards**: Optimized Twitter sharing
- ✅ **Structured Data**: JSON-LD schema for search engines
- ✅ **Sitemap**: Auto-generated XML sitemap
- ✅ **Robots.txt**: Search engine crawling instructions
- ✅ **Canonical URLs**: Prevent duplicate content issues

### Performance Features
- ✅ **Next.js Image Optimization**: WebP/AVIF format support
- ✅ **Lazy Loading**: Intersection Observer for sections
- ✅ **Code Splitting**: Automatic bundle optimization
- ✅ **Compression**: Gzip/Brotli compression enabled
- ✅ **Caching Headers**: Optimized cache control
- ✅ **Font Optimization**: Preconnect to Google Fonts
- ✅ **Security Headers**: CSP, HSTS, and other security measures

### Core Web Vitals
- ✅ **LCP**: Optimized Largest Contentful Paint
- ✅ **FID**: Minimized First Input Delay
- ✅ **CLS**: Reduced Cumulative Layout Shift
- ✅ **TTFB**: Fast Time to First Byte

## Development

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Analyze bundle size
npm run build && npx @next/bundle-analyzer
```

## SEO Checklist

- [x] Page titles optimized for search engines
- [x] Meta descriptions under 160 characters
- [x] Structured data (JSON-LD) implemented
- [x] Open Graph tags for social sharing
- [x] Twitter Card meta tags
- [x] Canonical URLs set
- [x] Sitemap.xml generated
- [x] Robots.txt configured
- [x] Image alt texts added
- [x] Semantic HTML structure
- [x] Mobile-friendly responsive design
- [x] Fast loading times (< 3 seconds)

## License

This project is for educational purposes only. Please respect content creators and use legal streaming services when available.
